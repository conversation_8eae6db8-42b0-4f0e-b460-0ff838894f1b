// ENS-specific error handling utilities
import { toast } from "sonner";

export interface ENSError {
  code: string;
  message: string;
  userMessage: string;
  recoveryAction?: string;
  context?: string;
}

export class ENSErrorHandler {
  private static errorMap: Record<string, ENSError> = {
    // Authentication Errors
    'AUTH_REQUIRED': {
      code: 'AUTH_REQUIRED',
      message: 'Authentication required',
      userMessage: 'Please log in to continue with ENS integration',
      recoveryAction: 'Login again'
    },
    'AUTH_EXPIRED': {
      code: 'AUTH_EXPIRED',
      message: 'Authentication token expired',
      userMessage: 'Your session has expired. Please log in again',
      recoveryAction: 'Refresh and login'
    },

    // Application Errors
    'APP_NOT_SELECTED': {
      code: 'APP_NOT_SELECTED',
      message: 'No application selected',
      userMessage: 'Please select an application before proceeding',
      recoveryAction: 'Select an application'
    },
    'APP_NOT_FOUND': {
      code: 'APP_NOT_FOUND',
      message: 'Application not found',
      userMessage: 'The selected application could not be found',
      recoveryAction: 'Select a different application'
    },

    // ENS Domain Errors
    'ENS_INVALID_NAME': {
      code: 'ENS_INVALID_NAME',
      message: 'Invalid ENS name format',
      userMessage: 'Please enter a valid ENS name ending with .eth',
      recoveryAction: 'Check ENS name format'
    },
    'ENS_NOT_OWNED': {
      code: 'ENS_NOT_OWNED',
      message: 'ENS domain not owned by connected wallet',
      userMessage: 'You must own this ENS domain to register it',
      recoveryAction: 'Connect the wallet that owns this domain'
    },
    'ENS_ALREADY_REGISTERED': {
      code: 'ENS_ALREADY_REGISTERED',
      message: 'ENS domain already registered',
      userMessage: 'This ENS domain is already registered for another application',
      recoveryAction: 'Use a different ENS domain'
    },
    'ENS_REGISTRATION_FAILED': {
      code: 'ENS_REGISTRATION_FAILED',
      message: 'ENS registration failed',
      userMessage: 'Failed to register ENS domain. Please try again',
      recoveryAction: 'Retry registration'
    },

    // Subname Errors
    'SUBNAME_INVALID': {
      code: 'SUBNAME_INVALID',
      message: 'Invalid subname format',
      userMessage: 'Subname can only contain lowercase letters, numbers, and hyphens',
      recoveryAction: 'Check subname format'
    },
    'SUBNAME_UNAVAILABLE': {
      code: 'SUBNAME_UNAVAILABLE',
      message: 'Subname not available',
      userMessage: 'This subname is already taken. Please choose another',
      recoveryAction: 'Try a different subname'
    },
    'SUBNAME_CLAIM_FAILED': {
      code: 'SUBNAME_CLAIM_FAILED',
      message: 'Subname claiming failed',
      userMessage: 'Failed to claim subname. Please try again',
      recoveryAction: 'Retry claiming'
    },

    // Wallet Errors
    'WALLET_NOT_CONNECTED': {
      code: 'WALLET_NOT_CONNECTED',
      message: 'Wallet not connected',
      userMessage: 'Please connect your wallet to continue',
      recoveryAction: 'Connect wallet'
    },
    'WALLET_WRONG_NETWORK': {
      code: 'WALLET_WRONG_NETWORK',
      message: 'Wrong network',
      userMessage: 'Please switch to the correct network',
      recoveryAction: 'Switch network'
    },
    'TRANSACTION_REJECTED': {
      code: 'TRANSACTION_REJECTED',
      message: 'Transaction rejected by user',
      userMessage: 'Transaction was cancelled. Please try again if you want to proceed',
      recoveryAction: 'Retry transaction'
    },
    'INSUFFICIENT_FUNDS': {
      code: 'INSUFFICIENT_FUNDS',
      message: 'Insufficient funds for transaction',
      userMessage: 'You don\'t have enough funds to complete this transaction',
      recoveryAction: 'Add funds to your wallet'
    },

    // Network Errors
    'NETWORK_ERROR': {
      code: 'NETWORK_ERROR',
      message: 'Network connection error',
      userMessage: 'Network error occurred. Please check your connection and try again',
      recoveryAction: 'Check connection and retry'
    },
    'API_ERROR': {
      code: 'API_ERROR',
      message: 'API request failed',
      userMessage: 'Service temporarily unavailable. Please try again later',
      recoveryAction: 'Try again later'
    },
    'TIMEOUT_ERROR': {
      code: 'TIMEOUT_ERROR',
      message: 'Request timeout',
      userMessage: 'Request timed out. Please try again',
      recoveryAction: 'Retry request'
    },

    // Generic Errors
    'UNKNOWN_ERROR': {
      code: 'UNKNOWN_ERROR',
      message: 'Unknown error occurred',
      userMessage: 'An unexpected error occurred. Please try again',
      recoveryAction: 'Retry operation'
    }
  };

  static handleError(error: any, context?: string): ENSError {
    console.error('ENS Error:', error, 'Context:', context);

    // Try to match error to known patterns
    const errorString = error?.message || error?.toString() || '';
    
    // Check for specific error patterns
    if (errorString.includes('401') || errorString.includes('Unauthorized')) {
      return this.getError('AUTH_EXPIRED', context);
    }
    
    if (errorString.includes('409') || errorString.includes('already registered')) {
      return this.getError('ENS_ALREADY_REGISTERED', context);
    }
    
    if (errorString.includes('not owned') || errorString.includes('ownership')) {
      return this.getError('ENS_NOT_OWNED', context);
    }
    
    if (errorString.includes('invalid') && errorString.includes('ens')) {
      return this.getError('ENS_INVALID_NAME', context);
    }
    
    if (errorString.includes('invalid') && errorString.includes('subname')) {
      return this.getError('SUBNAME_INVALID', context);
    }
    
    if (errorString.includes('unavailable') || errorString.includes('taken')) {
      return this.getError('SUBNAME_UNAVAILABLE', context);
    }
    
    if (errorString.includes('rejected') || errorString.includes('cancelled')) {
      return this.getError('TRANSACTION_REJECTED', context);
    }
    
    if (errorString.includes('insufficient')) {
      return this.getError('INSUFFICIENT_FUNDS', context);
    }
    
    if (errorString.includes('network') || errorString.includes('connection')) {
      return this.getError('NETWORK_ERROR', context);
    }
    
    if (errorString.includes('timeout')) {
      return this.getError('TIMEOUT_ERROR', context);
    }

    // Default to unknown error
    return this.getError('UNKNOWN_ERROR', context);
  }

  static getError(code: string, context?: string): ENSError {
    const error = this.errorMap[code] || this.errorMap['UNKNOWN_ERROR'];
    return {
      ...error,
      context
    };
  }

  static showErrorToast(error: ENSError) {
    toast.error(error.userMessage, {
      description: error.recoveryAction ? `Suggestion: ${error.recoveryAction}` : undefined,
      duration: 5000,
    });
  }

  static showSuccessToast(title: string, description?: string) {
    toast.success(title, {
      description,
      duration: 3000,
    });
  }

  static showInfoToast(title: string, description?: string) {
    toast.info(title, {
      description,
      duration: 4000,
    });
  }

  static showWarningToast(title: string, description?: string) {
    toast.warning(title, {
      description,
      duration: 4000,
    });
  }

  // Validation helpers
  static validateENSName(ensName: string): { isValid: boolean; error?: ENSError } {
    if (!ensName || typeof ensName !== 'string') {
      return {
        isValid: false,
        error: this.getError('ENS_INVALID_NAME')
      };
    }

    const trimmedName = ensName.trim().toLowerCase();
    
    if (!trimmedName.endsWith('.eth')) {
      return {
        isValid: false,
        error: this.getError('ENS_INVALID_NAME')
      };
    }

    // Basic ENS name validation
    const namePattern = /^[a-z0-9-]+\.eth$/;
    if (!namePattern.test(trimmedName)) {
      return {
        isValid: false,
        error: this.getError('ENS_INVALID_NAME')
      };
    }

    return { isValid: true };
  }

  static validateSubname(subname: string): { isValid: boolean; error?: ENSError } {
    if (!subname || typeof subname !== 'string') {
      return {
        isValid: false,
        error: this.getError('SUBNAME_INVALID')
      };
    }

    const trimmedSubname = subname.trim().toLowerCase();
    
    // Subname validation pattern
    const subnamePattern = /^[a-z0-9-]+$/;
    if (!subnamePattern.test(trimmedSubname)) {
      return {
        isValid: false,
        error: this.getError('SUBNAME_INVALID')
      };
    }

    // Check length constraints
    if (trimmedSubname.length < 1 || trimmedSubname.length > 63) {
      return {
        isValid: false,
        error: this.getError('SUBNAME_INVALID')
      };
    }

    return { isValid: true };
  }
}
