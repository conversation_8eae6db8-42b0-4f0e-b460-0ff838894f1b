// ENS Types and Interfaces for Crefy Connect Platform

export interface ENSRootRegistrationRequest {
  ens_name: string;
  contractAddress: string;
  chain: string;
  isActive: boolean;
}

export interface ENSRootRegistrationResponse {
  success: boolean;
  message: string;
  data: {
    name: string;
    contractAddress: string;
    isActive: boolean;
    ownerAppId: string;
  };
}

export interface SubnameAvailabilityRequest {
  subName: string;
  chain: string;
}

export interface SubnameAvailabilityResponse {
  success: boolean;
  message: string;
  available: boolean;
}

export interface SubnameClaimRequest {
  subName: string;
  chain: string;
  useAccountAbstraction?: boolean;
}

export interface TransactionDetails {
  status: string;
  address: string;
  hash: string;
  gasUsed: string;
  effectiveGasPrice: string;
  estimatedGas: string;
  estimatedCost: string;
  chainId: number;
}

export interface SubnameClaimResponse {
  success: boolean;
  message: string;
  data: TransactionDetails;
}

export interface ENSErrorDetails {
  functionName: string;
  args: string[];
  account: string;
  contractAddress: string;
  chainId: number;
  estimatedGas?: string;
  gasPrice?: string;
  estimatedCost?: string;
  accountBalance?: string;
  required?: string;
  shortBy?: string;
}

export interface ENSErrorResponse {
  success: false;
  message: string;
  error?: string;
  details?: ENSErrorDetails;
}

// Component State Interfaces
export interface ENSRootState {
  ensName: string;
  contractAddress: string;
  chain: string;
  isActive: boolean;
  isRegistering: boolean;
  registrationStatus: 'idle' | 'pending' | 'success' | 'error';
  error?: string;
}

export interface SubnameState {
  subName: string;
  chain: string;
  isAvailable: boolean | null;
  isChecking: boolean;
  isClaiming: boolean;
  claimStatus: 'idle' | 'pending' | 'success' | 'error';
  useAccountAbstraction: boolean;
  transactionHash?: string;
  error?: string;
}

export interface ENSConnection {
  id: string;
  projectId: string;
  ensName: string;
  owner: string;
  avatar?: string;
  connectedAt: string;
  isActive: boolean;
  contractAddress?: string;
  chain: string;
}

export interface ENSOwnershipData {
  owner: string | null;
  avatar: string | null;
  isOwned: boolean;
  resolver?: string;
  contentHash?: string;
  textRecords?: Record<string, string>;
}

// Smart Contract Interfaces
export interface ContractConfig {
  address: string;
  abi: any[];
  chainId: number;
}

export interface FactoryContractConfig extends ContractConfig {
  functions: {
    createSubnameRegistrar: string;
    getSubnameContractsByOwner: string;
    subnameContracts: string;
  };
}

export interface L1SubnameRegistrarConfig extends ContractConfig {
  functions: {
    claimName: string;
    isNameClaimed: string;
    viewAllSubnames: string;
    parentNode: string;
  };
}

export interface NameWrapperConfig extends ContractConfig {
  functions: {
    safeTransferFrom: string;
    ownerOf: string;
    isWrapped: string;
  };
}

// Validation Interfaces
export interface ENSValidationResult {
  isValid: boolean;
  error?: string;
  suggestions?: string[];
}

export interface SubnameValidationResult {
  isValid: boolean;
  error?: string;
  normalizedName?: string;
}

// Hook Return Types
export interface UseENSRootReturn {
  state: ENSRootState;
  registerRoot: (data: ENSRootRegistrationRequest) => Promise<void>;
  resetState: () => void;
}

export interface UseSubnameReturn {
  state: SubnameState;
  checkAvailability: (subName: string, chain: string) => Promise<void>;
  claimSubname: (data: SubnameClaimRequest) => Promise<void>;
  resetState: () => void;
}

export interface UseENSOwnershipReturn {
  ownershipData: ENSOwnershipData | null;
  isLoading: boolean;
  error: string | null;
  checkOwnership: (ensName: string) => Promise<void>;
  transferOwnership: (ensName: string, newOwner: string) => Promise<void>;
}

// API Response Wrappers
export type ENSApiResponse<T> = {
  success: true;
  data: T;
  message?: string;
} | ENSErrorResponse;

// Chain Configuration
export interface ChainConfig {
  id: number;
  name: string;
  network: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: {
    default: {
      http: string[];
    };
  };
  blockExplorers: {
    default: {
      name: string;
      url: string;
    };
  };
  contracts: {
    ensRegistry: string;
    ensUniversalResolver: string;
    multicall3: string;
  };
}

// Supported Chains for ENS
export type SupportedChain = 'ethereum' | 'sepolia' | 'base' | 'baseSepolia';

export interface ENSChainConfig {
  [key: string]: ChainConfig;
}

// Gas Estimation
export interface GasEstimation {
  gasLimit: string;
  gasPrice: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  estimatedCost: string;
  estimatedCostUSD?: string;
}

// Transaction Status
export type TransactionStatus = 'idle' | 'pending' | 'confirming' | 'confirmed' | 'failed';

export interface TransactionState {
  status: TransactionStatus;
  hash?: string;
  confirmations?: number;
  gasUsed?: string;
  effectiveGasPrice?: string;
  error?: string;
}

// ENS Records
export interface ENSRecord {
  key: string;
  value: string;
  type: 'text' | 'address' | 'contenthash';
}

export interface ENSProfile {
  name: string;
  address: string;
  avatar?: string;
  description?: string;
  email?: string;
  url?: string;
  twitter?: string;
  github?: string;
  discord?: string;
  telegram?: string;
  records: ENSRecord[];
}

// Component Props
export interface ENSRootRegistrationProps {
  applicationId: string;
  prefilledENSName?: string;
  prefilledContractAddress?: string;
  onSuccess?: (data: ENSRootRegistrationResponse['data']) => void;
  onError?: (error: string) => void;
  className?: string;
}

export interface SubnameClaimingProps {
  ensRoot: string;
  applicationId?: string;
  onSuccess?: (data: TransactionDetails) => void;
  onError?: (error: string) => void;
  className?: string;
  showAccountAbstraction?: boolean;
}

export interface ENSConnectionCardProps {
  connection: ENSConnection;
  onDisconnect?: (connectionId: string) => void;
  onTransfer?: (ensName: string) => void;
  className?: string;
}

// Form Data
export interface ENSRootForm {
  ensName: string;
  applicationId: string;
  chain: SupportedChain;
}

export interface SubnameClaimForm {
  subName: string;
  chain: SupportedChain;
  useAccountAbstraction: boolean;
}

// Constants
export const SUPPORTED_CHAINS: SupportedChain[] = ['ethereum', 'sepolia', 'base', 'baseSepolia'];

export const ENS_VALIDATION_RULES = {
  MIN_LENGTH: 3,
  MAX_LENGTH: 63,
  ALLOWED_CHARACTERS: /^[a-z0-9-]+$/,
  RESERVED_NAMES: ['www', 'mail', 'ftp', 'admin', 'root', 'api'],
} as const;

export const TRANSACTION_TIMEOUTS = {
  CONFIRMATION: 300000, // 5 minutes
  RECEIPT: 60000, // 1 minute
} as const;
