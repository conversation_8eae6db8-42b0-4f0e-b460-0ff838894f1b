// Wallet Authentication Service
// Handles wallet signature-based authentication for ENS endpoints

import { useSignMessage } from 'wagmi';
import { Address } from 'viem';

export interface WalletAuthToken {
  address: Address;
  signature: string;
  message: string;
  timestamp: number;
}

export interface WalletAuthResult {
  success: boolean;
  token?: string;
  error?: string;
}

export class WalletAuthService {
  /**
   * Generate a message for wallet signing
   * @param address - The wallet address
   * @param applicationId - The application ID
   * @param action - The action being performed (e.g., 'ens-registration')
   * @returns The message to be signed
   */
  static generateSignMessage(
    address: Address,
    applicationId: string,
    action: string = 'ens-registration'
  ): string {
    const timestamp = Date.now();
    const nonce = Math.random().toString(36).substring(2, 15);
    
    return `Crefy Connect - Wallet Authentication

Action: ${action}
Application ID: ${applicationId}
Wallet Address: ${address}
Timestamp: ${timestamp}
Nonce: ${nonce}

By signing this message, you authorize this action on the Crefy Connect platform.`;
  }

  /**
   * Create a wallet authentication token from signature
   * @param address - The wallet address
   * @param signature - The wallet signature
   * @param message - The original message that was signed
   * @returns Base64 encoded authentication token
   */
  static createAuthToken(
    address: Address,
    signature: string,
    message: string
  ): string {
    const authData: WalletAuthToken = {
      address,
      signature,
      message,
      timestamp: Date.now()
    };

    // Encode as base64 for transmission
    return btoa(JSON.stringify(authData));
  }

  /**
   * Verify if a wallet auth token is valid and not expired
   * @param token - The base64 encoded auth token
   * @param maxAge - Maximum age in milliseconds (default: 5 minutes)
   * @returns Whether the token is valid
   */
  static isTokenValid(token: string, maxAge: number = 5 * 60 * 1000): boolean {
    try {
      const authData: WalletAuthToken = JSON.parse(atob(token));
      const now = Date.now();
      
      // Check if token is expired
      if (now - authData.timestamp > maxAge) {
        return false;
      }

      // Basic validation
      return !!(authData.address && authData.signature && authData.message);
    } catch (error) {
      return false;
    }
  }

  /**
   * Extract address from wallet auth token
   * @param token - The base64 encoded auth token
   * @returns The wallet address or null if invalid
   */
  static getAddressFromToken(token: string): Address | null {
    try {
      const authData: WalletAuthToken = JSON.parse(atob(token));
      return authData.address;
    } catch (error) {
      return null;
    }
  }
}

/**
 * React hook for wallet authentication
 * @param applicationId - The application ID
 * @param action - The action being performed
 * @returns Wallet authentication utilities
 */
export function useWalletAuth(applicationId: string, action: string = 'ens-registration') {
  const { signMessageAsync, isPending: isSigning } = useSignMessage();

  const authenticateWallet = async (address: Address): Promise<WalletAuthResult> => {
    try {
      // Generate message to sign
      const message = WalletAuthService.generateSignMessage(address, applicationId, action);
      
      // Request wallet signature
      const signature = await signMessageAsync({ message });
      
      // Create authentication token
      const token = WalletAuthService.createAuthToken(address, signature, message);
      
      return {
        success: true,
        token
      };
    } catch (error) {
      console.error('Wallet authentication failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Wallet authentication failed'
      };
    }
  };

  return {
    authenticateWallet,
    isSigning,
    generateMessage: (address: Address) => 
      WalletAuthService.generateSignMessage(address, applicationId, action),
    isTokenValid: WalletAuthService.isTokenValid,
    getAddressFromToken: WalletAuthService.getAddressFromToken
  };
}
