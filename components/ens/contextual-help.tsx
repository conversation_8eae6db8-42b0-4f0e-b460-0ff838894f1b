'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  InfoIcon, 
  CheckCircleIcon, 
  ArrowRightIcon, 
  ExternalLinkIcon,
  LightbulbIcon,
  AlertCircleIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HelpAction {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  external?: boolean;
}

interface ContextualHelpProps {
  title: string;
  description: string;
  type?: 'info' | 'success' | 'warning' | 'tip';
  actions?: HelpAction[];
  benefits?: string[];
  requirements?: string[];
  nextSteps?: string[];
  className?: string;
}

export function ContextualHelp({
  title,
  description,
  type = 'info',
  actions = [],
  benefits = [],
  requirements = [],
  nextSteps = [],
  className
}: ContextualHelpProps) {
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          container: 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200',
          icon: <CheckCircleIcon className="h-5 w-5 text-green-600" />,
          titleColor: 'text-green-800',
          descriptionColor: 'text-green-700'
        };
      case 'warning':
        return {
          container: 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200',
          icon: <AlertCircleIcon className="h-5 w-5 text-yellow-600" />,
          titleColor: 'text-yellow-800',
          descriptionColor: 'text-yellow-700'
        };
      case 'tip':
        return {
          container: 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200',
          icon: <LightbulbIcon className="h-5 w-5 text-blue-600" />,
          titleColor: 'text-blue-800',
          descriptionColor: 'text-blue-700'
        };
      default:
        return {
          container: 'bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/30',
          icon: <InfoIcon className="h-5 w-5 text-[#4A148C]" />,
          titleColor: 'text-[#4A148C]',
          descriptionColor: 'text-[#4A148C]/80'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <Card className={cn(
      "backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300",
      styles.container,
      className
    )}>
      <CardHeader className="pb-3">
        <CardTitle className={cn("text-sm flex items-center gap-2 font-semibold", styles.titleColor)}>
          {styles.icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        {/* Description */}
        <p className={cn("text-sm leading-relaxed", styles.descriptionColor)}>
          {description}
        </p>

        {/* Requirements */}
        {requirements.length > 0 && (
          <div>
            <h4 className={cn("text-xs font-semibold mb-2", styles.titleColor)}>
              Requirements:
            </h4>
            <ul className="space-y-1">
              {requirements.map((requirement, index) => (
                <li key={index} className="flex items-start gap-2 text-xs">
                  <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className={styles.descriptionColor}>{requirement}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Benefits */}
        {benefits.length > 0 && (
          <div>
            <h4 className={cn("text-xs font-semibold mb-2", styles.titleColor)}>
              Benefits:
            </h4>
            <div className="flex flex-wrap gap-1">
              {benefits.map((benefit, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="bg-white/60 text-[#4A148C] text-xs border border-[#B497D6]/30"
                >
                  {benefit}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Next Steps */}
        {nextSteps.length > 0 && (
          <div>
            <h4 className={cn("text-xs font-semibold mb-2", styles.titleColor)}>
              Next Steps:
            </h4>
            <ol className="space-y-1">
              {nextSteps.map((step, index) => (
                <li key={index} className="flex items-start gap-2 text-xs">
                  <div className="w-4 h-4 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white rounded-full flex items-center justify-center text-xs font-medium shadow-sm flex-shrink-0 mt-0.5">
                    {index + 1}
                  </div>
                  <span className={styles.descriptionColor}>{step}</span>
                </li>
              ))}
            </ol>
          </div>
        )}

        {/* Actions */}
        {actions.length > 0 && (
          <div className="flex flex-col sm:flex-row gap-2 pt-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                size="sm"
                variant={action.variant || 'outline'}
                onClick={action.onClick}
                className={cn(
                  "text-xs",
                  action.variant === 'primary' && "bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25",
                  action.variant === 'outline' && "border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C]"
                )}
              >
                {action.label}
                {action.external ? (
                  <ExternalLinkIcon className="ml-1 h-3 w-3" />
                ) : (
                  <ArrowRightIcon className="ml-1 h-3 w-3" />
                )}
              </Button>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default ContextualHelp;
