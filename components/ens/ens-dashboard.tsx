'use client';

import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import {
  GlobeIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  RefreshCwIcon,
  AlertCircleIcon,
  TrendingUpIcon,
  UsersIcon,
  LinkIcon,
  SettingsIcon,
  CopyIcon,
  ArrowRightIcon
} from "lucide-react";
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import { ENSConnection } from "@/lib/types/ens";
import { ApplicationWithApiKey } from "@/lib/api";
import { ENSTransferOwnership } from "./ens-transfer-ownership";

export interface ENSDashboardProps {
  selectedApplication: ApplicationWithApiKey;
  ensConnections: ENSConnection[];
  onRefresh?: () => void;
  className?: string;
}

interface ENSStats {
  totalDomains: number;
  activeDomains: number;
  totalSubnames: number;
  recentActivity: number;
}

export function ENSDashboard({ 
  selectedApplication, 
  ensConnections, 
  onRefresh, 
  className = "" 
}: ENSDashboardProps) {
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<ENSStats>({
    totalDomains: 0,
    activeDomains: 0,
    totalSubnames: 0,
    recentActivity: 0
  });
  const [transferModalOpen, setTransferModalOpen] = useState<string | null>(null);
  const [selectedConnection, setSelectedConnection] = useState<ENSConnection | null>(null);

  // Filter connections for the selected application (memoized to prevent infinite loops)
  const appConnections = useMemo(() =>
    ensConnections.filter(conn => conn.projectId === selectedApplication.appId),
    [ensConnections, selectedApplication.appId]
  );

  // Calculate stats
  useEffect(() => {
    const totalDomains = appConnections.length;
    const activeDomains = appConnections.filter(conn => conn.isActive).length;
    // Subname counting feature to be implemented in future release
    const totalSubnames = 0;
    const recentActivity = appConnections.filter(conn => {
      const connectedDate = new Date(conn.connectedAt);
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      return connectedDate > weekAgo;
    }).length;

    setStats({
      totalDomains,
      activeDomains,
      totalSubnames,
      recentActivity
    });
  }, [appConnections]);

  const handleRefresh = useCallback(async () => {
    setLoading(true);
    try {
      // Refresh ENS connections data
      onRefresh?.();
      showToast({
        type: 'success',
        title: 'Data Refreshed',
        description: 'ENS dashboard data has been updated'
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: 'Refresh Failed',
        description: 'Failed to refresh ENS data'
      });
    } finally {
      setLoading(false);
    }
  }, [onRefresh, showToast]);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    showToast({
      type: 'success',
      title: 'Copied',
      description: `${label} copied to clipboard`
    });
  };

  const openEtherscan = (ensName: string) => {
    window.open(`https://etherscan.io/name-lookup-search?id=${ensName}`, '_blank');
  };

  // Handle transfer ownership
  const handleTransferOwnership = (connection: ENSConnection) => {
    setSelectedConnection(connection);
    setTransferModalOpen(connection.id);
  };

  // Handle transfer success
  const handleTransferSuccess = (txHash: string, newOwner: string) => {
    showToast({
      type: 'success',
      title: 'Ownership Transferred',
      description: `ENS ownership has been transferred successfully`
    });

    setTransferModalOpen(null);
    setSelectedConnection(null);

    // Refresh the connections list
    onRefresh?.();
  };

  // Handle transfer error
  const handleTransferError = (error: string) => {
    showToast({
      type: 'error',
      title: 'Transfer Failed',
      description: error
    });
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            ENS Dashboard
          </h2>
          <p className="text-gray-600 mt-1">
            Manage ENS domains for {selectedApplication.name}
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
        >
          <RefreshCwIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Domains</p>
                <p className="text-2xl font-bold text-[#4A148C]">{stats.totalDomains}</p>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-lg flex items-center justify-center">
                <GlobeIcon className="h-5 w-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Domains</p>
                <p className="text-2xl font-bold text-green-600">{stats.activeDomains}</p>
              </div>
              <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                <CheckCircleIcon className="h-5 w-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Subnames</p>
                <p className="text-2xl font-bold text-blue-600">{stats.totalSubnames}</p>
              </div>
              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <UsersIcon className="h-5 w-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Recent Activity</p>
                <p className="text-2xl font-bold text-orange-600">{stats.recentActivity}</p>
              </div>
              <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                <TrendingUpIcon className="h-5 w-5 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ENS Domains List */}
      {appConnections.length > 0 ? (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
              <GlobeIcon className="h-5 w-5 text-[#4A148C]" />
              Connected ENS Domains
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {appConnections.map((connection) => (
              <div
                key={connection.id}
                className="p-4 border border-[#B497D6]/20 rounded-xl bg-gradient-to-r from-white to-[#7B1FA2]/5 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-[#4A148C] text-lg">{connection.ensName}</h3>
                      <Badge 
                        variant={connection.isActive ? "default" : "secondary"}
                        className={connection.isActive 
                          ? "bg-green-100 text-green-800 border border-green-200" 
                          : "bg-gray-100 text-gray-600 border border-gray-200"
                        }
                      >
                        {connection.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Owner:</span>
                        <code className="ml-1 text-xs bg-gray-100 px-1 py-0.5 rounded">
                          {connection.owner.slice(0, 6)}...{connection.owner.slice(-4)}
                        </code>
                      </div>
                      <div>
                        <span className="font-medium">Chain:</span>
                        <span className="ml-1 capitalize">{connection.chain}</span>
                      </div>
                      <div>
                        <span className="font-medium">Connected:</span>
                        <span className="ml-1">{new Date(connection.connectedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      onClick={() => copyToClipboard(connection.ensName, 'ENS Name')}
                      variant="outline"
                      size="sm"
                      className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
                      title="Copy ENS Name"
                    >
                      <CopyIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => openEtherscan(connection.ensName)}
                      variant="outline"
                      size="sm"
                      className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
                      title="View on Etherscan"
                    >
                      <ExternalLinkIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => handleTransferOwnership(connection)}
                      variant="outline"
                      size="sm"
                      className="border-amber-300 text-amber-700 hover:bg-amber-50"
                      title="Transfer Ownership"
                    >
                      <ArrowRightIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      ) : (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardContent className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-full flex items-center justify-center">
              <GlobeIcon className="h-8 w-8 text-[#4A148C]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No ENS Domains Connected</h3>
            <p className="text-gray-600 mb-4">
              Connect your first ENS domain to start managing subnames for your users.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Transfer Ownership Modal */}
      {transferModalOpen && selectedConnection && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                  Transfer ENS Ownership
                </h2>
                <Button
                  onClick={() => {
                    setTransferModalOpen(null);
                    setSelectedConnection(null);
                  }}
                  variant="outline"
                  size="sm"
                  className="border-gray-300 text-gray-600 hover:bg-gray-100"
                >
                  ✕
                </Button>
              </div>

              <ENSTransferOwnership
                ensName={selectedConnection.ensName}
                currentOwner={selectedConnection.owner}
                onSuccess={handleTransferSuccess}
                onError={handleTransferError}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
