'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { useAccount } from 'wagmi';
import { 
  UserPlusIcon, 
  RefreshCwIcon, 
  CheckCircleIcon,
  AlertCircleIcon,
  ExternalLinkIcon,
  ZapIcon,
  CreditCardIcon,
  InfoIcon
} from "lucide-react";
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import {
  SubnameClaimingProps,
  SubnameClaimRequest,
  SupportedChain,
  SUPPORTED_CHAINS,
  TransactionStatus,
  ENS_VALIDATION_RULES
} from "@/lib/types/ens";
import { E<PERSON><PERSON>rrorHand<PERSON> } from "@/lib/ens-error-handler";

interface ClaimingState {
  subName: string;
  chain: SupportedChain;
  useAccountAbstraction: boolean;
  status: TransactionStatus;
  transactionHash?: string;
  error?: string;
}

export function SubnameClaiming({
  ensRoot,
  applicationId,
  onSuccess,
  onError,
  className = "",
  showAccountAbstraction = true
}: SubnameClaimingProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [claimingState, setClaimingState] = useState<ClaimingState>({
    subName: '',
    chain: 'sepolia',
    useAccountAbstraction: false,
    status: 'idle'
  });

  const [validationError, setValidationError] = useState<string | null>(null);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);

  // Validate subname format
  const validateSubname = (name: string): string | null => {
    if (!name) return null;
    
    if (name.length < ENS_VALIDATION_RULES.MIN_LENGTH) {
      return `Subname must be at least ${ENS_VALIDATION_RULES.MIN_LENGTH} characters long`;
    }
    
    if (name.length > ENS_VALIDATION_RULES.MAX_LENGTH) {
      return `Subname must be no more than ${ENS_VALIDATION_RULES.MAX_LENGTH} characters long`;
    }
    
    if (!ENS_VALIDATION_RULES.ALLOWED_CHARACTERS.test(name)) {
      return 'Subname can only contain lowercase letters, numbers, and hyphens';
    }
    
    if (name.startsWith('-') || name.endsWith('-')) {
      return 'Subname cannot start or end with a hyphen';
    }
    
    if (ENS_VALIDATION_RULES.RESERVED_NAMES.includes(name.toLowerCase())) {
      return 'This subname is reserved and cannot be used';
    }
    
    return null;
  };

  // Validate subname on input change
  useEffect(() => {
    const error = validateSubname(claimingState.subName);
    setValidationError(error);
    setIsAvailable(null); // Reset availability when subname changes
  }, [claimingState.subName]);

  const handleInputChange = (field: keyof ClaimingState, value: any) => {
    setClaimingState(prev => ({
      ...prev,
      [field]: field === 'subName' ? value.toLowerCase().replace(/[^a-z0-9-]/g, '') : value
    }));
  };

  const checkAvailability = async () => {
    if (!token || !claimingState.subName || validationError) return;

    setIsCheckingAvailability(true);
    try {
      const response = await apiService.checkSubnameAvailability(
        claimingState.subName, 
        claimingState.chain, 
        token
      );

      if (response.success && response.data) {
        setIsAvailable(response.data.available);
        if (!response.data.available) {
          showToast({
            type: 'error',
            title: 'Subname Not Available',
            description: `${claimingState.subName} is already taken`
          });
        }
      }
    } catch (error) {
      console.error('Availability check failed:', error);
      const ensError = ENSErrorHandler.handleError(error, 'Subname Availability Check');
      ENSErrorHandler.showErrorToast(ensError);
    } finally {
      setIsCheckingAvailability(false);
    }
  };

  // Auto-check availability when subname is valid
  useEffect(() => {
    if (claimingState.subName && !validationError && token) {
      const timeoutId = setTimeout(checkAvailability, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [claimingState.subName, claimingState.chain, validationError, token]);

  const handleClaim = async () => {
    if (!token) {
      showToast({
        type: 'error',
        title: 'Authentication Required',
        description: 'Please log in to claim a subname'
      });
      return;
    }

    if (!isConnected) {
      showToast({
        type: 'error',
        title: 'Wallet Not Connected',
        description: 'Please connect your wallet to claim a subname'
      });
      return;
    }

    if (!claimingState.subName || validationError) {
      showToast({
        type: 'error',
        title: 'Invalid Subname',
        description: validationError || 'Please enter a valid subname'
      });
      return;
    }

    if (isAvailable === false) {
      showToast({
        type: 'error',
        title: 'Subname Not Available',
        description: 'This subname is already taken'
      });
      return;
    }

    setClaimingState(prev => ({ ...prev, status: 'pending', error: undefined }));

    try {
      const claimData: SubnameClaimRequest = {
        subName: claimingState.subName,
        chain: claimingState.chain,
        useAccountAbstraction: claimingState.useAccountAbstraction
      };

      const response = await apiService.claimSubname(claimData, token, applicationId);

      if (response.success && response.data) {
        setClaimingState(prev => ({
          ...prev,
          status: 'confirmed',
          transactionHash: response.data.data.hash
        }));

        showToast({
          type: 'success',
          title: 'Subname Claimed Successfully',
          description: `${claimingState.subName} has been claimed and is now yours!`
        });

        onSuccess?.(response.data.data);

        // Reset form after successful claim
        setTimeout(() => {
          setClaimingState({
            subName: '',
            chain: 'sepolia',
            useAccountAbstraction: false,
            status: 'idle'
          });
          setIsAvailable(null);
        }, 3000);
      } else {
        throw new Error(response.error || 'Failed to claim subname');
      }
    } catch (error: any) {
      console.error('Subname claiming error:', error);
      const ensError = ENSErrorHandler.handleError(error, 'Subname Claiming');

      setClaimingState(prev => ({
        ...prev,
        status: 'failed',
        error: ensError.userMessage
      }));

      ENSErrorHandler.showErrorToast(ensError);
      onError?.(ensError.userMessage);
    }
  };

  const getFullSubname = () => {
    if (!claimingState.subName) return '';
    return ensRoot ? `${claimingState.subName}.${ensRoot}` : `${claimingState.subName}.[root].eth`;
  };

  const isFormValid = claimingState.subName && 
                     !validationError && 
                     isAvailable === true && 
                     isConnected;

  const getStatusColor = () => {
    switch (claimingState.status) {
      case 'confirmed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'pending': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (claimingState.status) {
      case 'confirmed': return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case 'failed': return <AlertCircleIcon className="h-4 w-4 text-red-600" />;
      case 'pending': return <RefreshCwIcon className="h-4 w-4 text-blue-600 animate-spin" />;
      default: return null;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 max-w-none overflow-hidden">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
            <UserPlusIcon className="h-5 w-5 sm:h-6 sm:w-6 text-[#4A148C]" />
            Claim ENS Subname
          </CardTitle>
          <p className="text-sm text-gray-600 mt-2 leading-relaxed">
            Claim a subname under {ensRoot || 'your ENS root domain'} for your users.
          </p>
        </CardHeader>

      <CardContent className="space-y-6">
        {/* Subname Input */}
        <div className="space-y-3">
          <Label htmlFor="claimSubname" className="text-sm font-medium text-[#4A148C]">
            Subname to Claim
          </Label>
          <Input
            id="claimSubname"
            type="text"
            placeholder="username"
            value={claimingState.subName}
            onChange={(e) => handleInputChange('subName', e.target.value)}
            disabled={claimingState.status === 'pending'}
            className={`${validationError ? 'border-red-300 focus:border-red-500' : ''}`}
          />
          
          {/* Preview */}
          <div className="text-sm text-gray-600">
            Preview: <span className="font-mono bg-gray-100 px-2 py-1 rounded">
              {getFullSubname()}
            </span>
          </div>

          {/* Validation Error */}
          {validationError && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertCircleIcon className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-800">{validationError}</span>
              </div>
            </div>
          )}

          {/* Availability Status */}
          {claimingState.subName && !validationError && (
            <div className={`p-3 rounded-lg border ${
              isCheckingAvailability 
                ? 'bg-blue-50 border-blue-200'
                : isAvailable === true 
                  ? 'bg-green-50 border-green-200'
                  : isAvailable === false
                    ? 'bg-red-50 border-red-200'
                    : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-2">
                {isCheckingAvailability ? (
                  <RefreshCwIcon className="h-4 w-4 text-blue-600 animate-spin" />
                ) : isAvailable === true ? (
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                ) : isAvailable === false ? (
                  <AlertCircleIcon className="h-4 w-4 text-red-600" />
                ) : null}
                <span className={`text-sm font-medium ${
                  isCheckingAvailability 
                    ? 'text-blue-800'
                    : isAvailable === true 
                      ? 'text-green-800'
                      : isAvailable === false
                        ? 'text-red-800'
                        : 'text-gray-800'
                }`}>
                  {isCheckingAvailability 
                    ? 'Checking availability...'
                    : isAvailable === true 
                      ? 'Available for claiming'
                      : isAvailable === false
                        ? 'Already taken'
                        : 'Enter a subname to check availability'
                  }
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Chain Selection */}
        <div className="space-y-3">
          <Label htmlFor="claimChain" className="text-sm font-medium text-[#4A148C]">
            Blockchain Network
          </Label>
          <Select
            id="claimChain"
            value={claimingState.chain}
            onChange={(e) => handleInputChange('chain', e.target.value as SupportedChain)}
            disabled={claimingState.status === 'pending'}
          >
            {SUPPORTED_CHAINS.map(chain => (
              <SelectOption key={chain} value={chain}>
                {chain.charAt(0).toUpperCase() + chain.slice(1)}
                {chain === 'sepolia' && (
                  <Badge variant="secondary" className="ml-2 text-xs">Testnet</Badge>
                )}
              </SelectOption>
            ))}
          </Select>
        </div>

        {/* Account Abstraction Option */}
        {showAccountAbstraction && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium text-[#4A148C]">
                Transaction Type
              </Label>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant={!claimingState.useAccountAbstraction ? "default" : "outline"}
                onClick={() => handleInputChange('useAccountAbstraction', false)}
                disabled={claimingState.status === 'pending'}
                className="flex items-center gap-2 justify-start p-4 h-auto"
              >
                <CreditCardIcon className="h-4 w-4" />
                <div className="text-left">
                  <div className="font-medium">Regular</div>
                  <div className="text-xs opacity-70">Pay gas fees</div>
                </div>
              </Button>
              <Button
                variant={claimingState.useAccountAbstraction ? "default" : "outline"}
                onClick={() => handleInputChange('useAccountAbstraction', true)}
                disabled={claimingState.status === 'pending'}
                className="flex items-center gap-2 justify-start p-4 h-auto"
              >
                <ZapIcon className="h-4 w-4" />
                <div className="text-left">
                  <div className="font-medium">Gasless</div>
                  <div className="text-xs opacity-70">No gas fees</div>
                </div>
              </Button>
            </div>
          </div>
        )}

        {/* Transaction Status */}
        {claimingState.status !== 'idle' && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon()}
              <span className={`font-medium ${getStatusColor()}`}>
                {claimingState.status === 'pending' && 'Claiming subname...'}
                {claimingState.status === 'confirmed' && 'Subname claimed successfully!'}
                {claimingState.status === 'failed' && 'Claiming failed'}
              </span>
            </div>
            
            {claimingState.transactionHash && (
              <div className="text-sm text-gray-600">
                Transaction: 
                <Button
                  variant="link"
                  size="sm"
                  className="p-0 ml-1 h-auto text-blue-600"
                  onClick={() => window.open(`https://sepolia.etherscan.io/tx/${claimingState.transactionHash}`, '_blank')}
                >
                  {claimingState.transactionHash.slice(0, 10)}...
                  <ExternalLinkIcon className="ml-1 h-3 w-3" />
                </Button>
              </div>
            )}
            
            {claimingState.error && (
              <div className="text-sm text-red-600 mt-1">
                {claimingState.error}
              </div>
            )}
          </div>
        )}

        {/* Claim Button */}
        <Button
          onClick={handleClaim}
          disabled={!isFormValid || claimingState.status === 'pending'}
          className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
        >
          {claimingState.status === 'pending' ? (
            <>
              <RefreshCwIcon className="mr-2 h-4 w-4 animate-spin" />
              Claiming Subname...
            </>
          ) : (
            <>
              <UserPlusIcon className="mr-2 h-4 w-4" />
              Claim Subname
              {claimingState.useAccountAbstraction && (
                <Badge variant="secondary" className="ml-2 bg-yellow-100 text-yellow-800">
                  Gasless
                </Badge>
              )}
            </>
          )}
        </Button>

        {/* Help Text */}
        <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <InfoIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-blue-800 font-medium mb-1">Claiming Process</p>
              <p className="text-blue-700 text-xs">
                {claimingState.useAccountAbstraction 
                  ? 'Gasless transactions are sponsored by the platform. No ETH required.'
                  : 'Regular transactions require ETH for gas fees. Make sure you have enough ETH in your wallet.'
                }
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
