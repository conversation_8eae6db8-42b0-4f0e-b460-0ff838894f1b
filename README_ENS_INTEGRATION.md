# ENS Integration - Crefy Connect Platform

## Quick Start

The ENS (Ethereum Name Service) integration enables developers to link their ENS domains to applications, providing decentralized identity and user-friendly addresses.

### Access the Integration
Navigate to: `/dashboard/dashboard/ens`

### Prerequisites
1. **Wallet Connection**: MetaMask or compatible Ethereum wallet
2. **ENS Domain**: Must own an ENS domain (e.g., `yourname.eth`)
3. **Application**: At least one application created on the platform
4. **Authentication**: Valid login session

## 4-Step Workflow

### 1. 🔗 Connect Wallet
- Click "Connect Wallet" button
- Approve wallet connection via RainbowKit
- **Auto-advances** to Step 2 when connected

### 2. 📱 Select Application
- Choose from your existing applications
- Applications loaded from `/apps` API endpoint
- **Auto-advances** to Step 3 when selected

### 3. 🏷️ Enter ENS Name
- Enter your ENS domain (e.g., `myname.eth`)
- System verifies ownership via blockchain
- **Auto-advances** to Step 4 when verified

### 4. 🔗 Link to Application
- Confirm linking ENS to selected application
- Creates connection via `/ens/roots` API
- **Completes** workflow when successful

## Key Features

### ✅ Real Blockchain Verification
- Uses viem/wagmi for actual ENS ownership checking
- Supports mainnet and testnet networks
- Real-time validation with user feedback

### ✅ Seamless Navigation
- Forward/backward navigation between steps
- Automatic state management and cleanup
- Progress indicators and breadcrumbs

### ✅ Production-Ready API Integration
- All endpoints use Crefy Connect API
- Proper authentication with Bearer tokens
- Application context via x-api-key headers

### ✅ Comprehensive Error Handling
- User-friendly error messages
- Recovery suggestions for common issues
- Robust fallback mechanisms

## Technical Architecture

### Frontend Components
```
app/dashboard/dashboard/ens/page.tsx          # Main workflow orchestrator
components/ens/ens-name-entry.tsx             # ENS input & verification
components/ens/ens-root-registration.tsx      # API integration & linking
components/ens/application-selection.tsx      # App selection interface
```

### Backend Services
```
lib/services/ens-verification.ts              # Blockchain verification
lib/api.ts                                    # API endpoint integration
lib/contracts/                                # Smart contract ABIs
lib/ens-error-handler.ts                      # Error management
```

### API Endpoints
- `GET /apps` - Retrieve user applications
- `POST /ens/roots` - Register ENS root domain
- `GET /ens/roots` - List registered domains
- `GET /ens/subnames/check-availability` - Check subname availability
- `POST /ens/subnames/claim` - Claim subnames

## Development Setup

### 1. Install Dependencies
```bash
pnpm install
```

### 2. Environment Configuration
Ensure these environment variables are set:
```env
NEXT_PUBLIC_API_BASE_URL=https://api.crefy-connect-v2.crefy.xyz/api/v1
```

### 3. Start Development Server
```bash
pnpm dev
```

### 4. Access ENS Integration
Navigate to: `http://localhost:3001/dashboard/dashboard/ens`

## Testing

### Test Account
- **Email**: <EMAIL>
- **Password**: 123456

### Test Workflow
1. Login with test account
2. Connect wallet (use testnet for development)
3. Create test application if none exist
4. Follow 4-step ENS integration workflow

### Verification Testing
- Use ENS domains you actually own
- Test on both mainnet and testnet
- Verify API calls in browser Network tab

## Common Issues & Solutions

### Issue: "ENS Verification Failed"
**Solution**: 
- Ensure wallet is connected
- Verify you own the ENS domain
- Check network connection

### Issue: "API Call Failed"
**Solution**:
- Verify authentication token
- Check application selection
- Review browser console for errors

### Issue: "Cannot Navigate Back"
**Solution**:
- State management automatically resets
- Use Previous/Next buttons
- Refresh page if needed

### Issue: "Step 4 Won't Complete"
**Solution**:
- Verify all previous steps completed
- Check ENS ownership verification
- Ensure application is selected

## Code Examples

### ENS Verification
```typescript
import { ENSVerificationService } from '@/lib/services/ens-verification';

const result = await ENSVerificationService.verifyOwnership(
  'myname.eth',
  walletAddress,
  1 // mainnet
);

if (result.isOwned) {
  // Proceed with linking
}
```

### API Integration
```typescript
import { apiService } from '@/lib/api';

const response = await apiService.registerENSRoot(
  {
    ens_name: 'myname.eth',
    contractAddress: '0x...',
    chain: 'sepolia',
    isActive: true
  },
  authToken,
  applicationId
);
```

## Design System

### Colors
- **Primary Gradient**: #4A148C → #7B1FA2
- **Background**: White with backdrop-blur
- **Borders**: [#B497D6]/20 opacity
- **Success**: Green variants
- **Error**: Red variants

### Components
- **Cards**: rounded-2xl with shadow effects
- **Buttons**: Gradient backgrounds with hover effects
- **Inputs**: Consistent border styling
- **Progress**: Step indicators and breadcrumbs

## File Structure

```
├── app/dashboard/dashboard/ens/
│   └── page.tsx                    # Main ENS integration page
├── components/ens/
│   ├── application-selection.tsx   # App selection component
│   ├── ens-name-entry.tsx         # ENS input & verification
│   ├── ens-root-registration.tsx  # Registration component
│   ├── ens-dashboard.tsx          # Post-integration dashboard
│   ├── step-progress.tsx          # Progress indicators
│   └── breadcrumb-navigation.tsx  # Navigation breadcrumbs
├── lib/
│   ├── api.ts                     # API service layer
│   ├── services/
│   │   └── ens-verification.ts    # Blockchain verification
│   ├── contracts/                 # Smart contract ABIs
│   ├── types/ens.ts              # TypeScript definitions
│   └── ens-error-handler.ts      # Error management
└── docs/
    ├── ENS_INTEGRATION_GUIDE.md   # Comprehensive guide
    └── README_ENS_INTEGRATION.md  # This file
```

## Support

### Documentation
- [Complete Integration Guide](./docs/ENS_INTEGRATION_GUIDE.md)
- [API Documentation](./docs/API.md)
- [Component Documentation](./docs/COMPONENTS.md)

### Debugging
1. **Browser Console**: Check for JavaScript errors
2. **Network Tab**: Inspect API calls and responses
3. **React DevTools**: Examine component state
4. **Wallet DevTools**: Review transaction details

### Contact
For technical support or questions about the ENS integration, please refer to the comprehensive documentation or contact the development team.

---

**Status**: ✅ Production Ready
**Last Updated**: 2025-01-14
**Version**: 1.0.0
