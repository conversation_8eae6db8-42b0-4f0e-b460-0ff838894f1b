"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Copy, Trash2, CheckCircle, AlertCircle, Edit, MoreVertical, Settings, X } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper"
import { useAuth } from "@/lib/auth-context"
import { apiService, ApplicationWithApiKey, CreateApplicationRequest, VerifyAppRequest, VerifyAppResponse } from "@/lib/api"
import { toast } from "sonner"

export default function ApplicationsPage() {
  const { token } = useAuth()
  const [applications, setApplications] = useState<ApplicationWithApiKey[]>([])
  const [loading, setLoading] = useState(true)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [verifyDialogOpen, setVerifyDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const [editingApp, setEditingApp] = useState<ApplicationWithApiKey | null>(null)
  
  // Create application form state
  const [newApp, setNewApp] = useState<CreateApplicationRequest>({
    name: "",
    description: "",
    redirectUrls: [""],
    iconUrl: ""
  })

  // Edit application form state
  const [editApp, setEditApp] = useState<CreateApplicationRequest>({
    name: "",
    description: "",
    redirectUrls: [""],
    iconUrl: ""
  })
  
  // Verify application form state
  const [verifyData, setVerifyData] = useState<VerifyAppRequest>({
    appId: "",
    apiKey: ""
  })
  const [verifyResult, setVerifyResult] = useState<{
    success: boolean;
    data?: VerifyAppResponse;
    error?: string;
  } | null>(null)

  const fetchApplications = useCallback(async () => {
    if (!token) {
      console.log('No token available for fetching applications')
      return
    }

    console.log('Fetching applications with token:', token?.substring(0, 20) + '...')

    try {
      setLoading(true)
      console.log('🔄 Calling apiService.getApplications...')
      const response = await apiService.getApplications(token)
      console.log('📥 Raw API response:', response)

      if (response.success) {
        // Handle successful response - response.data should contain the apps array directly
        const apps = response.data || []
        console.log('Apps array from API service:', apps)

        // Ensure each app has redirectUrls as an array
        const normalizedApps = Array.isArray(apps) ? apps.map(app => ({
          ...app,
          redirectUrls: Array.isArray(app.redirectUrls) ? app.redirectUrls : []
        })) : []
        console.log('Normalized apps:', normalizedApps)
        setApplications(normalizedApps)
      } else {
        console.error('Failed to fetch applications:', response.error)
        // Handle "No apps found" as a successful empty result, not an error
        if (response.error && response.error.includes('No apps found')) {
          setApplications([])
        } else if (response.error && !response.error.includes('401') && !response.error.includes('Unauthorized')) {
          toast.error(response.error || "Failed to fetch applications")
        }
      }
    } catch (error) {
      console.error('Error fetching applications:', error)
      toast.error("Failed to fetch applications")
    } finally {
      setLoading(false)
    }
  }, [token])

  useEffect(() => {
    fetchApplications()
  }, [fetchApplications])

  const handleCreateApplication = async () => {
    if (!token) {
      toast.error("Authentication required")
      return
    }

    if (!newApp.name.trim()) {
      toast.error("Application name is required")
      return
    }

    const validRedirectUrls = newApp.redirectUrls.filter(url => url.trim())
    if (validRedirectUrls.length === 0) {
      toast.error("At least one redirect URL is required")
      return
    }

    try {
      const response = await apiService.createApplication({
        name: newApp.name.trim(),
        description: newApp.description?.trim() || undefined,
        redirectUrls: validRedirectUrls,
        iconUrl: newApp.iconUrl?.trim() || undefined
      }, token)

      if (response.success && response.data) {
        // Extract app data from API response
        const newAppData = response.data
        console.log('✅ Created app data:', newAppData)

        toast.success("Application created successfully")
        setApplications([...applications, newAppData])
        setCreateDialogOpen(false)
        setNewApp({ name: "", description: "", redirectUrls: [""], iconUrl: "" })
      } else {
        toast.error(response.error || "Failed to create application")
      }
    } catch (error) {
      toast.error("Failed to create application")
    }
  }

  const handleVerifyApplication = async () => {
    if (!token) {
      toast.error("Authentication required")
      return
    }

    if (!verifyData.appId.trim() || !verifyData.apiKey.trim()) {
      toast.error("Both App ID and API Key are required")
      return
    }

    try {
      const response = await apiService.verifyApplication(verifyData)
      
      setVerifyResult(response)
      if (response.success && response.data?.isValid) {
        toast.success("Application credentials are valid")
      } else {
        toast.error("Application credentials are invalid")
      }
    } catch (error) {
      toast.error("Failed to verify application")
      setVerifyResult({ success: false, data: undefined })
      console.error('Error verifying application:', error)
    }
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard`)
  }


  const addRedirectUrlField = () => {
    setNewApp(prev => ({
      ...prev,
      redirectUrls: [...prev.redirectUrls, ""]
    }))
  }

  const updateRedirectUrl = (index: number, value: string) => {
    setNewApp(prev => ({
      ...prev,
      redirectUrls: prev.redirectUrls.map((url, i) => i === index ? value : url)
    }))
  }

  const removeRedirectUrl = (index: number) => {
    if (newApp.redirectUrls.length > 1) {
      setNewApp(prev => ({
        ...prev,
        redirectUrls: prev.redirectUrls.filter((_, i) => i !== index)
      }))
    }
  }

  // Edit form helper functions
  const addEditRedirectUrlField = () => {
    setEditApp(prev => ({
      ...prev,
      redirectUrls: [...prev.redirectUrls, ""]
    }))
  }

  const updateEditRedirectUrl = (index: number, value: string) => {
    setEditApp(prev => ({
      ...prev,
      redirectUrls: prev.redirectUrls.map((url, i) => i === index ? value : url)
    }))
  }

  const removeEditRedirectUrl = (index: number) => {
    if (editApp.redirectUrls.length > 1) {
      setEditApp(prev => ({
        ...prev,
        redirectUrls: prev.redirectUrls.filter((_, i) => i !== index)
      }))
    }
  }

  const handleEditApplication = (app: ApplicationWithApiKey) => {
    setEditingApp(app)
    setEditApp({
      name: app.name,
      description: app.description || "",
      redirectUrls: [...(app.redirectUrls || [])],
      iconUrl: app.iconUrl || ""
    })
    setEditDialogOpen(true)
  }

  const handleUpdateApplication = async () => {
    if (!token || !editingApp) {
      toast.error("Authentication required")
      return
    }

    if (!editApp.name.trim()) {
      toast.error("Application name is required")
      return
    }

    const validRedirectUrls = editApp.redirectUrls.filter(url => url.trim())
    if (validRedirectUrls.length === 0) {
      toast.error("At least one redirect URL is required")
      return
    }

    try {
      const response = await apiService.updateApplication(editingApp.appId, {
        name: editApp.name.trim(),
        description: editApp.description?.trim() || undefined,
        redirectUrls: validRedirectUrls,
        iconUrl: editApp.iconUrl?.trim() || undefined
      }, token)

      if (response.success && response.data) {
        // Extract app data from API response
        const updatedAppData = response.data
        console.log('✅ Updated app data:', updatedAppData)

        toast.success("Application updated successfully")
        setApplications(applications.map(app =>
          app.appId === editingApp.appId ? { ...app, ...updatedAppData } : app
        ))
        setEditDialogOpen(false)
        setEditingApp(null)
        setEditApp({ name: "", description: "", redirectUrls: [""], iconUrl: "" })
      } else {
        toast.error(response.error || "Failed to update application")
      }
    } catch {
      toast.error("Failed to update application")
    }
  }

  const handleDeleteApplication = async (appId: string) => {
    if (!token) {
      toast.error("Authentication required")
      return
    }

    if (!confirm("Are you sure you want to delete this application? This action cannot be undone.")) {
      return
    }

    try {
      const response = await apiService.deleteApplication(appId, token)
      
      if (response.success) {
        toast.success("Application deleted successfully")
        setApplications(applications.filter(app => app.appId !== appId))
      } else {
        toast.error(response.error || "Failed to delete application")
      }
    } catch (error) {
      toast.error("Failed to delete application")
      console.error('Error deleting application:', error)
    }
  }

  const headerActions = (
    <div className="flex gap-2">
      <Dialog open={verifyDialogOpen} onOpenChange={setVerifyDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="bg-white border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-[#7B1FA2]/5 hidden sm:flex">
            <CheckCircle className="w-4 h-4 mr-2" />
            <span className="hidden md:inline">Verify Credentials</span>
            <span className="md:hidden">Verify</span>
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-[95vw] sm:max-w-lg bg-white/95 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl">
          <DialogHeader>
            <DialogTitle className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Verify Application Credentials</DialogTitle>
            <DialogDescription className="text-gray-600">
              Verify if your application credentials are valid and active
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="verify-app-id" className="text-sm font-medium text-gray-700">App ID</Label>
              <Input
                id="verify-app-id"
                placeholder="app_1234567890_abcdef"
                value={verifyData.appId}
                onChange={(e) => setVerifyData(prev => ({ ...prev, appId: e.target.value }))}
                className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
              />
            </div>
            <div>
              <Label htmlFor="verify-api-key" className="text-sm font-medium text-gray-700">API Key</Label>
              <Input
                id="verify-api-key"
                type="password"
                placeholder="ak_1234567890_abcdefghijk"
                value={verifyData.apiKey}
                onChange={(e) => setVerifyData(prev => ({ ...prev, apiKey: e.target.value }))}
                className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
              />
            </div>
            {verifyResult && (
              <div className={`p-4 rounded-2xl border backdrop-blur-sm ${verifyResult.success && verifyResult.data?.isValid ? 'bg-green-50/80 border-green-200' : 'bg-red-50/80 border-red-200'}`}>
                <div className="flex items-center gap-2">
                  {verifyResult.success && verifyResult.data?.isValid ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`font-medium ${verifyResult.success && verifyResult.data?.isValid ? 'text-green-800' : 'text-red-800'}`}>
                    {verifyResult.success && verifyResult.data?.isValid ? 'Valid Credentials' : 'Invalid Credentials'}
                  </span>
                </div>
                {verifyResult.success && verifyResult.data?.isValid && verifyResult.data && (
                  <div className="mt-2 text-sm text-gray-600">
                    <p><strong>App ID:</strong> {verifyResult.data.appId}</p>
                    <p><strong>Developer ID:</strong> {verifyResult.data.developerId}</p>
                    <p><strong>Allowed Domains:</strong> {verifyResult.data.allowedDomains?.join(', ')}</p>
                  </div>
                )}
              </div>
            )}
            <Button
              onClick={handleVerifyApplication}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 transition-all duration-300"
            >
              Verify Credentials
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogTrigger asChild>
          <Button className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300">
            <Plus className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">New Application</span>
            <span className="sm:hidden">New</span>
          </Button>
        </DialogTrigger>
                  <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[80vh] overflow-y-auto bg-white/95 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl">
                    <DialogHeader>
                      <DialogTitle className="text-lg sm:text-xl bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Create New Application</DialogTitle>
                      <DialogDescription className="text-gray-600">
                        Create a new OAuth application to get client credentials
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 sm:space-y-6">
                      <div>
                        <Label htmlFor="app-name" className="text-sm font-medium text-gray-700">Application Name *</Label>
                        <Input
                          id="app-name"
                          placeholder="My Awesome App"
                          value={newApp.name}
                          onChange={(e) => setNewApp(prev => ({ ...prev, name: e.target.value }))}
                          className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                        />
                      </div>

                      <div>
                        <Label htmlFor="app-description" className="text-sm font-medium text-gray-700">Description</Label>
                        <Input
                          id="app-description"
                          placeholder="A description of my app"
                          value={newApp.description || ""}
                          onChange={(e) => setNewApp(prev => ({ ...prev, description: e.target.value }))}
                          className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                        />
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-gray-700">Redirect URLs *</Label>
                        <p className="text-xs sm:text-sm text-gray-600 mb-2 mt-1">
                          URLs where users will be redirected after authentication
                        </p>
                        {newApp.redirectUrls.map((url, index) => (
                          <div key={index} className="flex gap-2 mt-2">
                            <Input
                              placeholder="https://myapp.com/callback"
                              value={url}
                              onChange={(e) => updateRedirectUrl(index, e.target.value)}
                              className="border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                            />
                            {newApp.redirectUrls.length > 1 && (
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => removeRedirectUrl(index)}
                                className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addRedirectUrlField}
                          className="mt-3 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5 hover:border-[#4A148C]"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Redirect URL
                        </Button>
                      </div>

                      <div>
                        <Label htmlFor="app-icon" className="text-sm font-medium text-gray-700">Icon URL</Label>
                        <Input
                          id="app-icon"
                          placeholder="https://myapp.com/icon.png"
                          value={newApp.iconUrl || ""}
                          onChange={(e) => setNewApp(prev => ({ ...prev, iconUrl: e.target.value }))}
                          className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                        />
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3 pt-4">
                        <Button
                          onClick={handleCreateApplication}
                          className="flex-1 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
                        >
                          Create Application
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setCreateDialogOpen(false)}
                          className="flex-1 sm:flex-none border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5 hover:border-[#4A148C]"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
        </DialogContent>
      </Dialog>
    </div>
  )

  return (
    <DashboardLayoutWrapper title="Applications" actions={headerActions}>
      {/* Edit Application Dialog */}
                <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                  <DialogContent className="max-w-[95vw] sm:max-w-2xl max-h-[80vh] overflow-y-auto bg-white/95 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl">
                    <DialogHeader>
                      <DialogTitle className="text-lg sm:text-xl bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">Edit Application</DialogTitle>
                      <DialogDescription className="text-gray-600">
                        Update your application settings and redirect URLs
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 sm:space-y-6">
                      <div>
                        <Label htmlFor="edit-name" className="text-sm font-medium text-gray-700">Application Name *</Label>
                        <Input
                          id="edit-name"
                          value={editApp.name}
                          onChange={(e) => setEditApp({ ...editApp, name: e.target.value })}
                          placeholder="My Awesome App"
                          className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                        />
                      </div>

                      <div>
                        <Label htmlFor="edit-description" className="text-sm font-medium text-gray-700">Description</Label>
                        <Input
                          id="edit-description"
                          value={editApp.description || ""}
                          onChange={(e) => setEditApp({ ...editApp, description: e.target.value })}
                          placeholder="A description of my app"
                          className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                        />
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-gray-700">Redirect URLs *</Label>
                        <div className="space-y-2 mt-2">
                          {editApp.redirectUrls.map((url, index) => (
                            <div key={index} className="flex gap-2">
                              <Input
                                value={url}
                                onChange={(e) => updateEditRedirectUrl(index, e.target.value)}
                                placeholder="https://myapp.com/callback"
                                className="flex-1 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                              />
                              {editApp.redirectUrls.length > 1 && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeEditRedirectUrl(index)}
                                  className="px-3 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          ))}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addEditRedirectUrlField}
                          className="mt-3 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5 hover:border-[#4A148C]"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Redirect URL
                        </Button>
                      </div>

                      <div>
                        <Label htmlFor="edit-icon" className="text-sm font-medium text-gray-700">Icon URL</Label>
                        <Input
                          id="edit-icon"
                          value={editApp.iconUrl || ""}
                          onChange={(e) => setEditApp({ ...editApp, iconUrl: e.target.value })}
                          placeholder="https://myapp.com/icon.png"
                          className="mt-2 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                        />
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3 pt-4">
                        <Button
                          onClick={handleUpdateApplication}
                          className="flex-1 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
                        >
                          Update Application
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setEditDialogOpen(false)}
                          className="flex-1 sm:flex-none border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5 hover:border-[#4A148C]"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

          {/* Loading State */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A148C]"></div>
            </div>
          ) : applications.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center">
                <Settings className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications Yet</h3>
              <p className="text-gray-600 mb-6">Create your first OAuth application to get started</p>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {applications.map((app) => (
                <Card key={app.appId} className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-2xl transform transition-all duration-300 hover:-translate-y-1 rounded-2xl">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div className="min-w-0 flex-1">
                        <CardTitle className="text-lg sm:text-xl truncate bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">{app.name}</CardTitle>
                        <CardDescription className="text-xs sm:text-sm text-gray-600">
                          Created on {new Date(app.createdAt).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Badge variant="default" className="bg-green-100/80 backdrop-blur-sm text-green-800 text-xs border border-green-200">
                          Active
                        </Badge>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-[#7B1FA2]/10 text-[#4A148C]">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="bg-white/95 backdrop-blur-sm border border-[#B497D6]/20 rounded-xl">
                            <DropdownMenuItem onClick={() => handleEditApplication(app)} className="hover:bg-[#7B1FA2]/10 text-[#4A148C] rounded-lg">
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600 focus:text-red-600 hover:bg-red-50 rounded-lg"
                              onClick={() => handleDeleteApplication(app.appId)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3 sm:space-y-4 pt-3">
                    <div>
                      <Label className="text-xs sm:text-sm font-medium text-gray-700">App ID</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <code className="flex-1 p-2 bg-[#B497D6]/5 backdrop-blur-sm rounded-lg text-xs sm:text-sm font-mono border border-[#B497D6]/20 truncate text-[#4A148C]">
                          {app.appId}
                        </code>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => copyToClipboard(app.appId, "App ID")}
                          className="h-8 w-8 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/10 hover:border-[#4A148C]"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>


                    <div>
                      <Label className="text-xs sm:text-sm font-medium text-gray-700">Redirect URLs</Label>
                      <div className="mt-1 space-y-1">
                        {(app.redirectUrls || []).map((url, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <code className="flex-1 p-2 bg-[#B497D6]/5 backdrop-blur-sm rounded-lg text-xs sm:text-sm font-mono border border-[#B497D6]/20 truncate text-[#4A148C]">
                              {url}
                            </code>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => copyToClipboard(url, "Redirect URL")}
                              className="h-8 w-8 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/10 hover:border-[#4A148C]"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
    </DashboardLayoutWrapper>
  )
}